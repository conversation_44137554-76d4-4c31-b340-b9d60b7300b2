using ZeroDateStrat.Models;

namespace ZeroDateStrat.Models;

// Phase 3: Machine Learning & AI Integration Models

public class MLPrediction
{
    public string ModelName { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public decimal Confidence { get; set; }
    public decimal PredictedValue { get; set; }
    public Dictionary<string, decimal> Features { get; set; } = new();
    public string PredictionType { get; set; } = string.Empty; // "SignalQuality", "PriceDirection", "Volatility"
}

public class SignalQualityScore
{
    public string SignalId { get; set; } = string.Empty;
    public decimal MLScore { get; set; }
    public decimal TechnicalScore { get; set; }
    public decimal MarketConditionScore { get; set; }
    public decimal LiquidityScore { get; set; }
    public decimal CompositeScore { get; set; }
    public List<string> QualityFactors { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

public class AdaptiveParameters
{
    public string StrategyName { get; set; } = string.Empty;
    public DateTime LastOptimized { get; set; }
    public Dictionary<string, decimal> Parameters { get; set; } = new();
    public decimal PerformanceImprovement { get; set; }
    public int OptimizationCycles { get; set; }
    public MarketConditions OptimizedFor { get; set; } = new();
}

public class MarketConditions
{
    public decimal VixLevel { get; set; }
    public decimal SpxTrend { get; set; }
    public decimal VolumeProfile { get; set; }
    public decimal ImpliedVolatilityRank { get; set; }
    public string MarketSession { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

// Phase 3: Real-time Monitoring Models

public class LiveDashboardData
{
    public DateTime Timestamp { get; set; }
    public decimal AccountValue { get; set; }
    public decimal DayPnL { get; set; }
    public decimal UnrealizedPnL { get; set; }
    public int ActivePositions { get; set; }
    public List<LivePosition> Positions { get; set; } = new();
    public List<RiskAlert> ActiveAlerts { get; set; } = new();
    public MarketConditions CurrentMarket { get; set; } = new();
    public List<TradingSignal> PendingSignals { get; set; } = new();
}

public class LivePosition
{
    public string PositionId { get; set; } = string.Empty;
    public string Strategy { get; set; } = string.Empty;
    public string Symbol { get; set; } = string.Empty;
    public decimal CurrentValue { get; set; }
    public decimal UnrealizedPnL { get; set; }
    public decimal PnLPercent { get; set; }
    public TimeSpan TimeInPosition { get; set; }
    public decimal Delta { get; set; }
    public decimal Gamma { get; set; }
    public decimal Theta { get; set; }
    public decimal Vega { get; set; }
    public DateTime LastUpdate { get; set; }
}

public class AlertConfiguration
{
    public string AlertType { get; set; } = string.Empty;
    public decimal Threshold { get; set; }
    public bool IsEnabled { get; set; }
    public List<string> NotificationChannels { get; set; } = new();
    public TimeSpan CooldownPeriod { get; set; }
    public DateTime LastTriggered { get; set; }
}

public class NotificationChannel
{
    public string Type { get; set; } = string.Empty; // "Email", "SMS", "Slack", "Discord", "Console"
    public Dictionary<string, string> Configuration { get; set; } = new();
    public bool IsEnabled { get; set; }
    public int Priority { get; set; }
}

public enum AlertSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

// Phase 3: Production Infrastructure Models

public class CircuitBreakerState
{
    public string ServiceName { get; set; } = string.Empty;
    public CircuitBreakerStatus Status { get; set; }
    public int FailureCount { get; set; }
    public DateTime LastFailure { get; set; }
    public DateTime NextRetryTime { get; set; }
    public TimeSpan Timeout { get; set; }
    public List<string> RecentErrors { get; set; } = new();
}

public enum CircuitBreakerStatus
{
    Closed,
    Open,
    HalfOpen
}

public class HealthCheckResult
{
    public string ServiceName { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public string Status { get; set; } = string.Empty;
    public TimeSpan ResponseTime { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
    public List<string> Errors { get; set; } = new();
}

public class SystemMetrics
{
    public DateTime Timestamp { get; set; }
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public long NetworkBytesReceived { get; set; }
    public long NetworkBytesSent { get; set; }
    public int ActiveConnections { get; set; }
    public TimeSpan Uptime { get; set; }
    public Dictionary<string, double> CustomMetrics { get; set; } = new();
}

// Phase 3: Advanced Strategy Models

public class StrategyPerformanceProfile
{
    public string StrategyName { get; set; } = string.Empty;
    public Dictionary<string, decimal> MarketConditionPerformance { get; set; } = new();
    public decimal OptimalVixRange { get; set; }
    public decimal OptimalVolumeRange { get; set; }
    public TimeSpan OptimalTimeWindow { get; set; }
    public List<string> BestMarketConditions { get; set; } = new();
    public decimal AdaptationScore { get; set; }
    public DateTime LastAnalyzed { get; set; }
}

// MultiTimeframeAnalysis is already defined in MarketModels.cs

public class TechnicalIndicators
{
    public decimal RSI { get; set; }
    public decimal MACD { get; set; }
    public decimal MACDSignal { get; set; }
    public decimal BollingerUpper { get; set; }
    public decimal BollingerLower { get; set; }
    public decimal SMA20 { get; set; }
    public decimal SMA50 { get; set; }
    public decimal Volume { get; set; }
    public decimal VWAP { get; set; }
    public string Trend { get; set; } = string.Empty;
}

public class PortfolioOptimization
{
    public DateTime Timestamp { get; set; }
    public Dictionary<string, decimal> StrategyAllocations { get; set; } = new();
    public decimal ExpectedReturn { get; set; }
    public decimal ExpectedRisk { get; set; }
    public decimal SharpeRatio { get; set; }
    public List<string> OptimizationConstraints { get; set; } = new();
    public string OptimizationMethod { get; set; } = string.Empty;
    public decimal ConfidenceLevel { get; set; }
}

// Phase 3: Database Models

public class TradeRecord
{
    public int Id { get; set; }
    public string TradeId { get; set; } = string.Empty;
    public string Strategy { get; set; } = string.Empty;
    public string Symbol { get; set; } = string.Empty;
    public DateTime OpenTime { get; set; }
    public DateTime? CloseTime { get; set; }
    public decimal OpenPrice { get; set; }
    public decimal? ClosePrice { get; set; }
    public decimal Quantity { get; set; }
    public decimal RealizedPnL { get; set; }
    public decimal UnrealizedPnL { get; set; }
    public string Status { get; set; } = string.Empty;
    public string ExitReason { get; set; } = string.Empty;
    public decimal MaxProfit { get; set; }
    public decimal MaxLoss { get; set; }
    public TimeSpan HoldingPeriod { get; set; }
    public string MarketConditions { get; set; } = string.Empty; // JSON serialized
    public decimal SignalQuality { get; set; }
}

public class PerformanceSnapshot
{
    public int Id { get; set; }
    public DateTime Timestamp { get; set; }
    public decimal AccountValue { get; set; }
    public decimal DayPnL { get; set; }
    public decimal WeekPnL { get; set; }
    public decimal MonthPnL { get; set; }
    public decimal YearPnL { get; set; }
    public decimal MaxDrawdown { get; set; }
    public decimal WinRate { get; set; }
    public decimal SharpeRatio { get; set; }
    public int TotalTrades { get; set; }
    public int WinningTrades { get; set; }
    public int LosingTrades { get; set; }
    public string StrategyBreakdown { get; set; } = string.Empty; // JSON serialized
}
