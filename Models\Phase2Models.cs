using ZeroDateStrat.Models;

namespace ZeroDateStrat.Models;

// Phase 2: Advanced Risk Management Models

public enum RiskLevel
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public class RiskAssessment
{
    public DateTime Timestamp { get; set; }
    public RiskLevel OverallRiskLevel { get; set; }
    public decimal RiskScore { get; set; }
    public decimal PortfolioValue { get; set; }
    public decimal AvailableBuyingPower { get; set; }
    public List<string> RiskFactors { get; set; } = new();
    public GreeksRiskAssessment GreeksRisk { get; set; } = new();
    public ConcentrationRisk ConcentrationRisk { get; set; } = new();
    public LiquidityRisk LiquidityRisk { get; set; } = new();
}

public class GreeksRiskAssessment
{
    public DateTime Timestamp { get; set; }
    public RiskLevel RiskLevel { get; set; }
    public decimal PortfolioDelta { get; set; }
    public decimal PortfolioGamma { get; set; }
    public decimal PortfolioTheta { get; set; }
    public decimal PortfolioVega { get; set; }
    public List<string> RiskFactors { get; set; } = new();
}

public class ConcentrationRisk
{
    public DateTime Timestamp { get; set; }
    public RiskLevel RiskLevel { get; set; }
    public string MostConcentratedSymbol { get; set; } = string.Empty;
    public decimal ConcentrationPercentage { get; set; }
    public List<string> RiskFactors { get; set; } = new();
}

public class LiquidityRisk
{
    public DateTime Timestamp { get; set; }
    public RiskLevel RiskLevel { get; set; }
    public decimal IlliquidityPercentage { get; set; }
    public List<string> IlliquidPositions { get; set; } = new();
    public List<string> RiskFactors { get; set; } = new();
}

public class RiskMetrics
{
    public DateTime Timestamp { get; set; }
    public decimal PortfolioValue { get; set; }
    public decimal AvailableBuyingPower { get; set; }
    public decimal DayTradingBuyingPower { get; set; }
    public decimal PortfolioDelta { get; set; }
    public decimal PortfolioGamma { get; set; }
    public decimal PortfolioTheta { get; set; }
    public decimal PortfolioVega { get; set; }
    public int NumberOfPositions { get; set; }
    public decimal LargestPositionSize { get; set; }
}

public class RiskAlert
{
    public string AlertType { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public RiskLevel Severity { get; set; }
    public DateTime CreatedTime { get; set; }
    public DateTime ExpiryTime { get; set; }
}

// Phase 2: Enhanced Position Management Models

public class ManagedPosition
{
    public string PositionId { get; set; } = string.Empty;
    public string Strategy { get; set; } = string.Empty;
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public List<PositionLeg> Legs { get; set; } = new();
    public decimal OpenCredit { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal UnrealizedPnL { get; set; }
    public decimal ProfitTarget { get; set; }
    public decimal StopLoss { get; set; }
    public DateTime OpenTime { get; set; }
    public DateTime? CloseTime { get; set; }
    public PositionStatus Status { get; set; }
    public List<PositionAdjustment> Adjustments { get; set; } = new();
}

public class PositionLeg
{
    public string Symbol { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal OpenPrice { get; set; }
    public decimal CurrentPrice { get; set; }
    public OrderSide Side { get; set; }
    public decimal Delta { get; set; }
    public decimal Gamma { get; set; }
    public decimal Theta { get; set; }
    public decimal Vega { get; set; }
}

public class PositionAdjustment
{
    public DateTime Timestamp { get; set; }
    public string AdjustmentType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PnLImpact { get; set; }
}



// Phase 2: Enhanced Trading Execution Models

public class ExecutionReport
{
    public string SignalId { get; set; } = string.Empty;
    public string Strategy { get; set; } = string.Empty;
    public DateTime ExecutionTime { get; set; }
    public ExecutionStatus Status { get; set; }
    public List<LegExecution> LegExecutions { get; set; } = new();
    public decimal TotalFillPrice { get; set; }
    public decimal Slippage { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}

public class LegExecution
{
    public string Symbol { get; set; } = string.Empty;
    public int RequestedQuantity { get; set; }
    public int FilledQuantity { get; set; }
    public decimal RequestedPrice { get; set; }
    public decimal FillPrice { get; set; }
    public DateTime FillTime { get; set; }
    public string OrderId { get; set; } = string.Empty;
}

public enum ExecutionStatus
{
    Pending,
    PartiallyFilled,
    Filled,
    Cancelled,
    Rejected,
    Failed
}

// Phase 2: Real-time Monitoring Models

public class PortfolioSnapshot
{
    public DateTime Timestamp { get; set; }
    public decimal TotalValue { get; set; }
    public decimal DayPnL { get; set; }
    public decimal TotalPnL { get; set; }
    public decimal BuyingPower { get; set; }
    public int ActivePositions { get; set; }
    public Dictionary<string, decimal> GreeksBySymbol { get; set; } = new();
    public List<PositionSummary> TopPositions { get; set; } = new();
}

public class PositionSummary
{
    public string Symbol { get; set; } = string.Empty;
    public string Strategy { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal PnL { get; set; }
    public decimal DaysToExpiration { get; set; }
    public RiskLevel RiskLevel { get; set; }
}

// Phase 2: Market Data Models

public class RealTimeQuote
{
    public string Symbol { get; set; } = string.Empty;
    public decimal Bid { get; set; }
    public decimal Ask { get; set; }
    public decimal Last { get; set; }
    public long Volume { get; set; }
    public DateTime Timestamp { get; set; }
    public decimal ImpliedVolatility { get; set; }
    public decimal Delta { get; set; }
    public decimal Gamma { get; set; }
    public decimal Theta { get; set; }
    public decimal Vega { get; set; }
}

public class MarketDataStream
{
    public string Symbol { get; set; } = string.Empty;
    public List<RealTimeQuote> Quotes { get; set; } = new();
    public DateTime LastUpdate { get; set; }
    public bool IsConnected { get; set; }
}

// Phase 2: Performance Analytics Models

public class PerformanceMetrics
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal TotalReturn { get; set; }
    public decimal AnnualizedReturn { get; set; }
    public decimal Sharpe { get; set; }
    public decimal MaxDrawdown { get; set; }
    public decimal WinRate { get; set; }
    public decimal AverageWin { get; set; }
    public decimal AverageLoss { get; set; }
    public decimal ProfitFactor { get; set; }
    public int TotalTrades { get; set; }
    public int WinningTrades { get; set; }
    public int LosingTrades { get; set; }
    public Dictionary<string, decimal> StrategyPerformance { get; set; } = new();
}

public class TradeAnalysis
{
    public string TradeId { get; set; } = string.Empty;
    public string Strategy { get; set; } = string.Empty;
    public DateTime EntryTime { get; set; }
    public DateTime? ExitTime { get; set; }
    public decimal EntryPrice { get; set; }
    public decimal ExitPrice { get; set; }
    public decimal PnL { get; set; }
    public decimal HoldingPeriod { get; set; }
    public string ExitReason { get; set; } = string.Empty;
    public MarketRegime EntryRegime { get; set; } = new();
    public MarketRegime ExitRegime { get; set; } = new();
}
