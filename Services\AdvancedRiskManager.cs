using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

// Phase 2: Advanced Risk Management Interface
public interface IAdvancedRiskManager
{
    Task<RiskAssessment> AssessPortfolioRiskAsync();
    Task<bool> ValidateTradeRiskAsync(TradingSignal signal);
    Task<decimal> CalculateOptimalPositionSizeAsync(TradingSignal signal);
    Task<Models.RiskMetrics> GetRealTimeRiskMetricsAsync();
    Task<bool> CheckDrawdownLimitsAsync();
    Task<List<RiskAlert>> GetActiveRiskAlertsAsync();
    Task<bool> ShouldHaltTradingAsync();
    Task<GreeksRiskAssessment> AssessGreeksRiskAsync();
    Task<ConcentrationRisk> AssessConcentrationRiskAsync();
    Task<LiquidityRisk> AssessLiquidityRiskAsync();
}

public class AdvancedRiskManager : IAdvancedRiskManager
{
    private readonly ILogger<AdvancedRiskManager> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;
    private readonly IMarketRegimeAnalyzer _marketRegimeAnalyzer;
    private readonly List<RiskAlert> _activeAlerts = new();

    public AdvancedRiskManager(
        ILogger<AdvancedRiskManager> logger,
        IConfiguration configuration,
        IAlpacaService alpacaService,
        IMarketRegimeAnalyzer marketRegimeAnalyzer)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
        _marketRegimeAnalyzer = marketRegimeAnalyzer;
    }

    public async Task<RiskAssessment> AssessPortfolioRiskAsync()
    {
        try
        {
            _logger.LogInformation("Conducting comprehensive portfolio risk assessment");

            var assessment = new RiskAssessment
            {
                Timestamp = DateTime.UtcNow,
                OverallRiskLevel = RiskLevel.Low
            };

            // Get account information
            var account = await _alpacaService.GetAccountAsync();
            if (account == null)
            {
                assessment.OverallRiskLevel = RiskLevel.High;
                assessment.RiskFactors.Add("Unable to retrieve account information");
                return assessment;
            }

            // Calculate portfolio metrics
            var portfolioValue = (decimal)account.Equity;
            var buyingPower = (decimal)account.BuyingPower;
            var dayTradingBuyingPower = (decimal)account.DayTradingBuyingPower;

            assessment.PortfolioValue = portfolioValue;
            assessment.AvailableBuyingPower = buyingPower;

            // Assess Greeks risk
            var greeksRisk = await AssessGreeksRiskAsync();
            assessment.GreeksRisk = greeksRisk;

            // Assess concentration risk
            var concentrationRisk = await AssessConcentrationRiskAsync();
            assessment.ConcentrationRisk = concentrationRisk;

            // Assess liquidity risk
            var liquidityRisk = await AssessLiquidityRiskAsync();
            assessment.LiquidityRisk = liquidityRisk;

            // Calculate overall risk score
            var riskScore = CalculateOverallRiskScore(greeksRisk, concentrationRisk, liquidityRisk);
            assessment.RiskScore = riskScore;

            // Determine overall risk level
            assessment.OverallRiskLevel = riskScore switch
            {
                < 30 => RiskLevel.Low,
                < 60 => RiskLevel.Medium,
                < 80 => RiskLevel.High,
                _ => RiskLevel.Critical
            };

            // Add risk factors based on assessment
            if (riskScore > 70)
                assessment.RiskFactors.Add($"High overall risk score: {riskScore:F1}");

            if (buyingPower < portfolioValue * 0.1m)
                assessment.RiskFactors.Add("Low buying power relative to portfolio value");

            _logger.LogInformation($"Portfolio risk assessment complete: {assessment.OverallRiskLevel} (Score: {riskScore:F1})");

            return assessment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error conducting portfolio risk assessment");
            return new RiskAssessment
            {
                Timestamp = DateTime.UtcNow,
                OverallRiskLevel = RiskLevel.High,
                RiskFactors = { "Error during risk assessment" }
            };
        }
    }

    public async Task<bool> ValidateTradeRiskAsync(TradingSignal signal)
    {
        try
        {
            _logger.LogInformation($"Validating trade risk for {signal.Strategy} on {signal.UnderlyingSymbol}");

            // Check if trading should be halted
            if (await ShouldHaltTradingAsync())
            {
                _logger.LogWarning("Trading halted due to risk conditions");
                return false;
            }

            // Validate position size
            var maxPositionSize = _configuration.GetValue<decimal>("Trading:MaxPositionSize", 10000);
            if (signal.MaxRisk > maxPositionSize)
            {
                _logger.LogWarning($"Position size {signal.MaxRisk:C} exceeds maximum {maxPositionSize:C}");
                return false;
            }

            // Validate risk-reward ratio
            var minRiskReward = _configuration.GetValue<decimal>("Trading:RiskRewardThreshold", 0.15m);
            if (signal.RiskRewardRatio < minRiskReward)
            {
                _logger.LogWarning($"Risk-reward ratio {signal.RiskRewardRatio:F2} below minimum {minRiskReward:F2}");
                return false;
            }

            // Check portfolio concentration
            var concentrationRisk = await AssessConcentrationRiskAsync();
            if (concentrationRisk.RiskLevel == RiskLevel.High && 
                signal.UnderlyingSymbol == concentrationRisk.MostConcentratedSymbol)
            {
                _logger.LogWarning($"High concentration risk in {signal.UnderlyingSymbol}");
                return false;
            }

            // Check Greeks limits
            var greeksRisk = await AssessGreeksRiskAsync();
            if (greeksRisk.RiskLevel == RiskLevel.High)
            {
                _logger.LogWarning("Portfolio Greeks risk is high");
                return false;
            }

            // Check market regime alignment
            var regime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
            if (regime.VolatilityRegime == VolatilityRegime.High)
            {
                _logger.LogWarning("High volatility regime - restricting new trades");
                return false;
            }

            _logger.LogInformation($"Trade risk validation passed for {signal.Strategy}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error validating trade risk for signal {signal.Id}");
            return false;
        }
    }

    public async Task<decimal> CalculateOptimalPositionSizeAsync(TradingSignal signal)
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            if (account == null) return 0;

            var portfolioValue = (decimal)account.Equity;
            var riskPerTrade = _configuration.GetValue<decimal>("Trading:RiskPerTrade", 0.02m);
            var maxPositionSize = _configuration.GetValue<decimal>("Trading:MaxPositionSize", 10000);

            // Calculate position size based on risk per trade
            var riskAmount = portfolioValue * riskPerTrade;
            var positionSize = Math.Min(riskAmount / Math.Max(signal.MaxRisk, 1), maxPositionSize);

            // Adjust based on market regime
            var regime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
            var regimeAdjustment = regime.VolatilityRegime switch
            {
                VolatilityRegime.Low => 1.0m,
                VolatilityRegime.Medium => 0.8m,
                VolatilityRegime.High => 0.5m,
                _ => 0.3m
            };

            positionSize *= regimeAdjustment;

            // Adjust based on portfolio risk
            var portfolioRisk = await AssessPortfolioRiskAsync();
            var riskAdjustment = portfolioRisk.OverallRiskLevel switch
            {
                RiskLevel.Low => 1.0m,
                RiskLevel.Medium => 0.8m,
                RiskLevel.High => 0.5m,
                _ => 0.2m
            };

            positionSize *= riskAdjustment;

            _logger.LogInformation($"Optimal position size for {signal.Strategy}: {positionSize:C} (Risk: {riskAmount:C}, Regime: {regime.VolatilityRegime})");

            return Math.Max(0, positionSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating optimal position size");
            return 0;
        }
    }

    public async Task<Models.RiskMetrics> GetRealTimeRiskMetricsAsync()
    {
        try
        {
            var metrics = new Models.RiskMetrics
            {
                Timestamp = DateTime.UtcNow
            };

            var account = await _alpacaService.GetAccountAsync();
            if (account != null)
            {
                metrics.PortfolioValue = (decimal)account.Equity;
                metrics.AvailableBuyingPower = (decimal)account.BuyingPower;
                metrics.DayTradingBuyingPower = (decimal)account.DayTradingBuyingPower;
            }

            // Get portfolio Greeks
            var greeks = await _alpacaService.GetPortfolioGreeksAsync();
            metrics.PortfolioDelta = greeks.GetValueOrDefault("Delta", 0);
            metrics.PortfolioGamma = greeks.GetValueOrDefault("Gamma", 0);
            metrics.PortfolioTheta = greeks.GetValueOrDefault("Theta", 0);
            metrics.PortfolioVega = greeks.GetValueOrDefault("Vega", 0);

            // Calculate risk metrics
            var positions = await _alpacaService.GetPositionsAsync();
            metrics.NumberOfPositions = positions.Count;
            metrics.LargestPositionSize = positions.Any() ? 
                positions.Max(p => Math.Abs(p.Quantity * (decimal)p.MarketValue)) : 0;

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting real-time risk metrics");
            return new Models.RiskMetrics { Timestamp = DateTime.UtcNow };
        }
    }

    public async Task<bool> CheckDrawdownLimitsAsync()
    {
        try
        {
            var account = await _alpacaService.GetAccountAsync();
            if (account == null) return false;

            var currentEquity = (decimal)account.Equity;
            var dayTradeCount = (int)account.DayTradeCount;
            var maxDailyLoss = _configuration.GetValue<decimal>("Trading:MaxDailyLoss", 500);

            // Check daily loss limit
            var dayPnL = (decimal)(account.Equity - account.LastEquity);
            if (dayPnL < -maxDailyLoss)
            {
                _logger.LogWarning($"Daily loss limit exceeded: {dayPnL:C} < -{maxDailyLoss:C}");
                return true;
            }

            // Check day trade limit
            var maxDayTrades = _configuration.GetValue<int>("Trading:MaxDayTrades", 3);
            if (dayTradeCount >= maxDayTrades)
            {
                _logger.LogWarning($"Day trade limit reached: {dayTradeCount} >= {maxDayTrades}");
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking drawdown limits");
            return true; // Err on the side of caution
        }
    }

    public async Task<List<RiskAlert>> GetActiveRiskAlertsAsync()
    {
        // Clean up expired alerts
        _activeAlerts.RemoveAll(a => a.ExpiryTime < DateTime.UtcNow);

        // Add new alerts based on current conditions
        await UpdateRiskAlerts();

        return _activeAlerts.ToList();
    }

    public async Task<bool> ShouldHaltTradingAsync()
    {
        try
        {
            // Check drawdown limits
            if (await CheckDrawdownLimitsAsync())
                return true;

            // Check market conditions
            var regime = await _marketRegimeAnalyzer.GetCurrentRegimeAsync();
            if (regime.VolatilityRegime == VolatilityRegime.High && regime.Confidence < 0.3m)
                return true;

            // Check portfolio risk
            var portfolioRisk = await AssessPortfolioRiskAsync();
            if (portfolioRisk.OverallRiskLevel == RiskLevel.Critical)
                return true;

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if trading should be halted");
            return true; // Halt trading on error
        }
    }

    public async Task<GreeksRiskAssessment> AssessGreeksRiskAsync()
    {
        try
        {
            var assessment = new GreeksRiskAssessment
            {
                Timestamp = DateTime.UtcNow
            };

            var greeks = await _alpacaService.GetPortfolioGreeksAsync();
            assessment.PortfolioDelta = greeks.GetValueOrDefault("Delta", 0);
            assessment.PortfolioGamma = greeks.GetValueOrDefault("Gamma", 0);
            assessment.PortfolioTheta = greeks.GetValueOrDefault("Theta", 0);
            assessment.PortfolioVega = greeks.GetValueOrDefault("Vega", 0);

            // Define risk thresholds
            var deltaThreshold = _configuration.GetValue<decimal>("Risk:MaxPortfolioDelta", 100);
            var gammaThreshold = _configuration.GetValue<decimal>("Risk:MaxPortfolioGamma", 50);
            var vegaThreshold = _configuration.GetValue<decimal>("Risk:MaxPortfolioVega", 200);

            // Assess risk levels
            var deltaRisk = Math.Abs(assessment.PortfolioDelta) / deltaThreshold;
            var gammaRisk = Math.Abs(assessment.PortfolioGamma) / gammaThreshold;
            var vegaRisk = Math.Abs(assessment.PortfolioVega) / vegaThreshold;

            var maxRisk = Math.Max(Math.Max(deltaRisk, gammaRisk), vegaRisk);

            assessment.RiskLevel = maxRisk switch
            {
                < 0.5m => RiskLevel.Low,
                < 0.8m => RiskLevel.Medium,
                < 1.0m => RiskLevel.High,
                _ => RiskLevel.Critical
            };

            if (deltaRisk > 0.8m)
                assessment.RiskFactors.Add($"High delta exposure: {assessment.PortfolioDelta:F1}");
            if (gammaRisk > 0.8m)
                assessment.RiskFactors.Add($"High gamma exposure: {assessment.PortfolioGamma:F1}");
            if (vegaRisk > 0.8m)
                assessment.RiskFactors.Add($"High vega exposure: {assessment.PortfolioVega:F1}");

            return assessment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing Greeks risk");
            return new GreeksRiskAssessment
            {
                Timestamp = DateTime.UtcNow,
                RiskLevel = RiskLevel.High
            };
        }
    }

    public async Task<ConcentrationRisk> AssessConcentrationRiskAsync()
    {
        try
        {
            var risk = new ConcentrationRisk
            {
                Timestamp = DateTime.UtcNow
            };

            var positions = await _alpacaService.GetPositionsAsync();
            if (!positions.Any())
            {
                risk.RiskLevel = RiskLevel.Low;
                return risk;
            }

            // Calculate position concentrations
            var totalValue = positions.Sum(p => Math.Abs(p.Quantity * (decimal)p.MarketValue));
            var concentrations = new Dictionary<string, decimal>();

            foreach (var position in positions)
            {
                var symbol = GetUnderlyingSymbol(position.Symbol);
                var value = Math.Abs(position.Quantity * (decimal)position.MarketValue);

                if (concentrations.ContainsKey(symbol))
                    concentrations[symbol] += value;
                else
                    concentrations[symbol] = value;
            }

            // Find highest concentration
            if (concentrations.Any())
            {
                var maxConcentration = concentrations.OrderByDescending(c => c.Value).First();
                risk.MostConcentratedSymbol = maxConcentration.Key;
                risk.ConcentrationPercentage = totalValue > 0 ? maxConcentration.Value / totalValue : 0;

                // Assess risk level
                risk.RiskLevel = risk.ConcentrationPercentage switch
                {
                    < 0.3m => RiskLevel.Low,
                    < 0.5m => RiskLevel.Medium,
                    < 0.7m => RiskLevel.High,
                    _ => RiskLevel.Critical
                };

                if (risk.ConcentrationPercentage > 0.5m)
                    risk.RiskFactors.Add($"High concentration in {risk.MostConcentratedSymbol}: {risk.ConcentrationPercentage:P1}");
            }

            return risk;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing concentration risk");
            return new ConcentrationRisk
            {
                Timestamp = DateTime.UtcNow,
                RiskLevel = RiskLevel.High
            };
        }
    }

    public async Task<LiquidityRisk> AssessLiquidityRiskAsync()
    {
        try
        {
            var risk = new LiquidityRisk
            {
                Timestamp = DateTime.UtcNow
            };

            var positions = await _alpacaService.GetPositionsAsync();
            var illiquidPositions = 0;
            var totalPositions = positions.Count;

            foreach (var position in positions)
            {
                // Simple liquidity check based on position size and symbol
                if (IsIlliquidPosition(position))
                {
                    illiquidPositions++;
                    risk.IlliquidPositions.Add(position.Symbol);
                }
            }

            risk.IlliquidityPercentage = totalPositions > 0 ? (decimal)illiquidPositions / totalPositions : 0;

            risk.RiskLevel = risk.IlliquidityPercentage switch
            {
                < 0.2m => RiskLevel.Low,
                < 0.4m => RiskLevel.Medium,
                < 0.6m => RiskLevel.High,
                _ => RiskLevel.Critical
            };

            if (risk.IlliquidityPercentage > 0.3m)
                risk.RiskFactors.Add($"High illiquidity: {risk.IlliquidityPercentage:P1} of positions");

            return risk;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing liquidity risk");
            return new LiquidityRisk
            {
                Timestamp = DateTime.UtcNow,
                RiskLevel = RiskLevel.High
            };
        }
    }

    // Helper methods
    private decimal CalculateOverallRiskScore(GreeksRiskAssessment greeksRisk, ConcentrationRisk concentrationRisk, LiquidityRisk liquidityRisk)
    {
        var greeksScore = (int)greeksRisk.RiskLevel * 25;
        var concentrationScore = (int)concentrationRisk.RiskLevel * 25;
        var liquidityScore = (int)liquidityRisk.RiskLevel * 25;

        return (greeksScore + concentrationScore + liquidityScore) / 3m;
    }

    private async Task UpdateRiskAlerts()
    {
        try
        {
            // Check for new risk conditions and add alerts
            var portfolioRisk = await AssessPortfolioRiskAsync();

            if (portfolioRisk.OverallRiskLevel >= RiskLevel.High)
            {
                var existingAlert = _activeAlerts.FirstOrDefault(a => a.AlertType == "PortfolioRisk");
                if (existingAlert == null)
                {
                    _activeAlerts.Add(new RiskAlert
                    {
                        AlertType = "PortfolioRisk",
                        Message = $"Portfolio risk level is {portfolioRisk.OverallRiskLevel}",
                        Severity = portfolioRisk.OverallRiskLevel,
                        CreatedTime = DateTime.UtcNow,
                        ExpiryTime = DateTime.UtcNow.AddHours(1)
                    });
                }
            }

            // Check drawdown
            if (await CheckDrawdownLimitsAsync())
            {
                var existingAlert = _activeAlerts.FirstOrDefault(a => a.AlertType == "Drawdown");
                if (existingAlert == null)
                {
                    _activeAlerts.Add(new RiskAlert
                    {
                        AlertType = "Drawdown",
                        Message = "Drawdown limits exceeded",
                        Severity = RiskLevel.Critical,
                        CreatedTime = DateTime.UtcNow,
                        ExpiryTime = DateTime.UtcNow.AddHours(24)
                    });
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating risk alerts");
        }
    }

    private string GetUnderlyingSymbol(string optionSymbol)
    {
        // Extract underlying symbol from option symbol
        if (optionSymbol.Contains("SPX") || optionSymbol.Contains("SPY"))
            return optionSymbol.Contains("SPX") ? "SPX" : "SPY";

        // For other symbols, take first 3-4 characters
        return optionSymbol.Length > 3 ? optionSymbol.Substring(0, 3) : optionSymbol;
    }

    private bool IsIlliquidPosition(Alpaca.Markets.IPosition position)
    {
        // Simple illiquidity check - in production, you'd use real volume/spread data
        var positionValue = Math.Abs(position.Quantity * (decimal)position.MarketValue);

        // Consider large positions or exotic symbols as potentially illiquid
        return positionValue > 50000 || !IsLiquidSymbol(position.Symbol);
    }

    private bool IsLiquidSymbol(string symbol)
    {
        // List of liquid symbols
        var liquidSymbols = new[] { "SPY", "SPX", "QQQ", "IWM", "AAPL", "MSFT", "TSLA" };
        return liquidSymbols.Any(s => symbol.Contains(s));
    }
}
